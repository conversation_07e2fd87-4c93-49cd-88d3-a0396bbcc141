# WNN-MRNN网络参数传递流图与数据维度 (基于实际代码分析)

## 引言

WNN-MRNN (小波神经网络-Mamba递归神经网络) 是一种用于调制识别的深度学习模型，它结合了小波分解和基于Mamba的递归神经网络。本文档详细描述了该网络的参数传递流程、数据维度变化以及各组件间的交互。通过分解信号到不同频率域并使用Mamba状态空间模型处理这些分量，模型能够有效捕获调制信号的时频特征。

## 1. 参数传递总览图与数据维度 (以RML数据集num_levels=2为例)

```
WNN_MRNN
├── 输入参数 (基于config.yaml中RML数据集配置):
│   ├── in_channels=2         # 输入通道数（I/Q信号）
│   ├── num_classes=11        # 分类类别数（RML数据集11种调制方式）
│   ├── wavelet_dim=256       # 小波分解输出通道数（RML数据集配置）
│   ├── rnn_dim=256           # RNN隐藏层维度，同时也是Mamba特征维度
│   ├── num_layers=4          # MMRNNCell的层数（RML数据集配置）
│   ├── msb_depth=1           # 每个MMRNNCell中MSB块的数量（默认配置）
│   ├── num_levels=2          # 小波分解层数，决定了高频分量的数量
│   │                         # 当num_levels=2时，产生2个高频分量+1个低频分量=3个总分量
│   ├── d_state=16            # Mamba状态空间模型的状态空间维度
│   ├── d_conv=4              # Mamba卷积核大小
│   ├── expand=2              # Mamba扩展因子
│   └── drop_rate=0.159       # Dropout比率（RML数据集配置）
│
├── 🔥各数据集类别数配置🔥:
│   ├── RML数据集: num_classes=11        # 8PSK, AM-DSB, AM-SSB, BPSK, CPFSK, GFSK, PAM4, QAM16, QAM64, QPSK, WBFM
│   ├── RML2018.01a: num_classes=24      # OOK, 4ASK, 8ASK, BPSK, QPSK, 8PSK, 16PSK, 32PSK, 16APSK, 32APSK, 64APSK, 128APSK, 16QAM, 32QAM, 64QAM, 128QAM, 256QAM, AM-SSB-WC, AM-SSB-SC, AM-DSB-WC, AM-DSB-SC, FM, GMSK, OQPSK
│   ├── HisarMod数据集: num_classes=26   # BPSK, QPSK, 8PSK, 16PSK, 32PSK, 64PSK, 4QAM, 8QAM, 16QAM, 32QAM, 64QAM, 128QAM, 256QAM, 2FSK, 4FSK, 8FSK, 16FSK, 4PAM, 8PAM, 16PAM, AM-DSB, AM-DSB-SC, AM-USB, AM-LSB, FM, PM
│   └── TorchSig数据集: num_classes=25   # BPSK, QPSK, 8PSK, 16PSK, 32PSK, 64PSK, 16QAM, 32QAM, 64QAM, 256QAM, 2FSK, 4FSK, 8FSK, 16FSK, 4ASK, 8ASK, 16ASK, 32ASK, 64ASK, AM-DSB, AM-DSB-SC, AM-USB, AM-LSB, FM, OOK
│
├── 输入数据维度: [B, 2, 128]  # 批次大小B, 2通道(I/Q), 序列长度128（RML数据集）
│                            # 2对应in_channels参数，128为RML数据集序列长度
│
├── 初始卷积层处理:
│   ├── 输入预处理: [B, 2, 128] -> [B, 1, 2, 128]  # 添加通道维度用于2D卷积
│   ├── Conv1(2D): [B, 1, 2, 128] -> [B, 64, 1, 128]  # 2D卷积(kernel_size=(2,7))处理I/Q通道
│   ├── 维度调整: [B, 64, 1, 128] -> [B, 64, 128]     # 去除多余维度
│   ├── Conv2(1D): [B, 64, 128] -> [B, 64, 128]       # 1D卷积(kernel_size=5)提取局部特征
│   └── Conv3(1D): [B, 64, 128] -> [B, 64, 128]       # 1D卷积(kernel_size=3)进一步提取特征
│
├── 小波分解 (基于提升方案):    # 分解层数为num_levels=2，产生2个高频分量和1个低频分量
│   ├── 第1级分解:
│   │   ├── 分裂: [B, 64, 128] -> 偶数[B, 64, 64] + 奇数[B, 64, 64]
│   │   ├── 更新: 低频1 = 偶数 + U1(奇数) -> [B, 64, 64]
│   │   └── 预测: 高频1 = 奇数 - P1(低频1) -> [B, 64, 64]
│   ├── 第2级分解:
│   │   ├── 分裂: [B, 64, 64] -> 偶数[B, 64, 32] + 奇数[B, 64, 32]
│   │   ├── 更新: 低频2 = 偶数 + U2(奇数) -> [B, 64, 32]
│   │   └── 预测: 高频2 = 奇数 - P2(低频2) -> [B, 64, 32]
│   └── 分解结果:
│       ├── 高频分量1: [B, 64, 64]   # 第一级高频分量
│       ├── 高频分量2: [B, 64, 32]   # 第二级高频分量
│       └── 低频分量: [B, 64, 32]    # 最终低频分量
│
├── 特征提取:             # 通道数由wavelet_dim=256决定
│   ├── 高频分量1: [B, 64, 64] -> [B, 256, 64]   # 通道数从64变为wavelet_dim=256
│   ├── 高频分量2: [B, 64, 32] -> [B, 256, 32]   # 通道数从64变为wavelet_dim=256
│   └── 低频分量: [B, 64, 32] -> [B, 256, 32]    # 通道数从64变为wavelet_dim=256
│
├── 维度重排:             # 交换通道维度和序列维度，为MRNN处理准备
│   ├── 高频分量1: [B, 256, 64] → [B, 64, 256]   # 转置，256是特征维度(wavelet_dim)
│   ├── 高频分量2: [B, 256, 32] → [B, 32, 256]   # 转置，256是特征维度(wavelet_dim)
│   └── 低频分量: [B, 256, 32] → [B, 32, 256]    # 转置，256是特征维度(wavelet_dim)
│
├── 分量列表准备: components_list = [高频1, 高频2, 低频]  # 保持各分量原始序列长度
│                 component_lengths = [64, 32, 32]        # 记录各分量的序列长度
│
├─→ MMRNNClassifier
│   ├── 接收参数:
│   │   ├── input_dim=wavelet_dim=256   # 从WNN_MRNN接收，决定输入特征维度
│   │   ├── hidden_dim=rnn_dim=256      # 从WNN_MRNN接收，决定隐藏状态维度
│   │   ├── num_components=num_levels+1=3 # 从WNN_MRNN接收，决定分量数量(2+1=3)
│   │   ├── num_layers=num_layers=4     # 从WNN_MRNN接收，决定MRNN层数
│   │   ├── msb_depth=msb_depth=1       # 从WNN_MRNN接收，控制每个MMRNNCell中MSB块的数量
│   │   ├── num_classes=num_classes=11  # 从WNN_MRNN接收，决定输出类别数
│   │   ├── d_state=d_state=16          # 从WNN_MRNN接收，决定Mamba状态空间维度
│   │   ├── d_conv=4                    # 从WNN_MRNN接收，决定Mamba卷积核大小
│   │   ├── expand=2                    # 从WNN_MRNN接收，决定Mamba特征扩展倍数
│   │   └── dropout=drop_rate=0.159     # 从WNN_MRNN接收，决定dropout比率
│   │
│   ├── 分量处理流程 (新的分量列表处理方式):
│   │   ├── 输入: components_list = [高频1[B,64,256], 高频2[B,32,256], 低频[B,32,256]]
│   │   ├── 初始化: layer_hidden_states = [None, None, None, None]  # 4层MMRNNCell的隐藏状态
│   │   │
│   │   ├── 分量循环处理 (c=0到c=2，共3个分量):
│   │   │   ├── 高频分量1(c=0): [B, 64, 256]   # 第一个处理的分量，序列长度64
│   │   │   ├── 高频分量2(c=1): [B, 32, 256]   # 第二个处理的分量，序列长度32
│   │   │   └── 低频分量(c=2): [B, 32, 256]    # 最后处理的分量，序列长度32
│   │   │
│   │   └── 每个分量的处理步骤:
│   │       ├── 输入投影: [B, S_c, 256] -> [B, S_c, 256]  # 投影到hidden_dim=256维度
│   │       │                                              # S_c为当前分量的序列长度
│   │       │
│   │       ├─→ 逐层MMRNNCell处理 (4层):
│   │       │   ├─→ MMRNNCell (第1层)
│   │       │   │   ├── 接收参数:
│   │       │   │   │   ├── hidden_dim=rnn_dim=256      # 从MMRNNClassifier接收，决定隐藏层维度
│   │       │   │   │   ├── msb_depth=msb_depth=1       # 从MMRNNClassifier接收，控制MSB块的数量
│   │       │   │   │   ├── drop_path=dropout=0.159     # 从MMRNNClassifier接收，控制丢弃率
│   │       │   │   │   ├── norm_layer=nn.LayerNorm     # 默认值，决定归一化层类型
│   │       │   │   │   ├── d_state=d_state=16          # 从MMRNNClassifier接收，控制状态空间维度
│   │       │   │   │   ├── d_conv=d_conv=4             # 从MMRNNClassifier接收，控制卷积核大小
│   │       │   │   │   └── expand=expand=2             # 从MMRNNClassifier接收，控制特征扩展倍数
│   │       │   │   │
│   │       │   │   ├── 输入数据: [B, S_c, 256]         # 批次大小B, 序列长度S_c, 特征维度256
│   │       │   │   │                                   # S_c为当前分量的序列长度，256由hidden_dim决定
│   │       │   │   │
│   │       │   │   ├── 🔥隐藏状态长度自适应机制🔥:
│   │       │   │   │   ├── 分量1(高频1): S_c=64, hidden_states=None
│   │       │   │   │   │   └── 初始化: hx=[B,64,256], cx=[B,64,256]
│   │       │   │   │   ├── 分量2(高频2): S_c=32, hidden_states来自分量1[B,64,256]
│   │       │   │   │   │   ├── 长度不匹配检测: 64 ≠ 32
│   │       │   │   │   │   ├── 创建适配器: Linear(64, 32) 键名"64_to_32"
│   │       │   │   │   │   ├── 维度转换: [B,64,256] -> [B,256,64] -> [B,256,32] -> [B,32,256]
│   │       │   │   │   │   └── 调整后: hx=[B,32,256], cx=[B,32,256] ✓匹配当前输入
│   │       │   │   │   └── 分量3(低频): S_c=32, hidden_states来自分量2[B,32,256]
│   │       │   │   │       ├── 长度匹配检测: 32 = 32 ✓
│   │       │   │   │       └── 直接使用: hx=[B,32,256], cx=[B,32,256]
│   │       │   │   │
│   │       │   │   ├─→ MSB (msb_depth=1个块)
│   │       │   │   │   ├── 接收参数:
│   │       │   │   │   │   ├── hidden_dim=rnn_dim=256      # 从MMRNNCell接收，决定隐藏层维度
│   │       │   │   │   │   ├── drop_path=drop_path=0.159   # 从MMRNNCell接收，控制丢弃率
│   │       │   │   │   │   ├── norm_layer=norm_layer       # 从MMRNNCell接收，决定归一化层类型
│   │       │   │   │   │   ├── d_state=d_state=16          # 从MMRNNCell接收，控制状态空间维度
│   │       │   │   │   │   ├── d_conv=d_conv=4             # 从MMRNNCell接收，控制卷积核大小
│   │       │   │   │   │   └── expand=expand=2             # 从MMRNNCell接收，控制特征扩展倍数
│   │       │   │   │   │
│   │       │   │   │   ├── 输入数据: [B, S_c, 256]         # 批次大小B, 序列长度S_c, 特征维度256
│   │       │   │   │   │
│   │       │   │   │   ├── 隐藏状态融合:
│   │       │   │   │   │   ├── 如果hx不为None: 拼接输入和隐藏状态 [B, S_c, 256*2]
│   │       │   │   │   │   ├── 线性投影: [B, S_c, 256*2] -> [B, S_c, 256]
│   │       │   │   │   │   └── 如果hx为None: 直接使用输入 [B, S_c, 256]
│   │       │   │   │   │
│   │       │   │   │   └─→ MSSBlock (父类) -> Mamba
│   │       │   │   │       ├── 层归一化: [B, S_c, 256] -> [B, S_c, 256]
│   │       │   │   │       ├── Mamba处理: [B, S_c, 256] -> [B, S_c, 256]
│   │       │   │   │       │   ├── d_model=hidden_dim=256      # 内部模型维度
│   │       │   │   │       │   ├── d_state=16                  # 状态空间维度
│   │       │   │   │       │   ├── d_conv=4                    # 卷积核大小
│   │       │   │   │       │   ├── expand=2                    # 特征扩展因子
│   │       │   │   │       │   └── d_inner=d_model*expand=256*2=512  # 内部扩展维度
│   │       │   │   │       ├── Dropout: [B, S_c, 256] -> [B, S_c, 256]
│   │       │   │   │       └── 残差连接: [B, S_c, 256] -> [B, S_c, 256]
│   │       │   │   │
│   │       │   │   ├── 门控机制:
│   │       │   │   │   ├── 门控系数: Ft = sigmoid(o_t) -> [B, S_c, 256]
│   │       │   │   │   ├── 候选状态: cell = tanh(o_t) -> [B, S_c, 256]
│   │       │   │   │   ├── 单元状态更新: Ct = Ft * (cx + cell) -> [B, S_c, 256]
│   │       │   │   │   └── 隐藏状态更新: Ht = Ft * tanh(Ct) -> [B, S_c, 256]
│   │       │   │   │
│   │       │   │   └── 输出: Ht[B, S_c, 256], 新隐藏状态(Ht, Ct)
│   │       │   │       # 注意: S_c根据当前分量变化 (64->32->32)
│   │       │   │
│   │       │   ├─→ MMRNNCell (第2层) - 结构与第1层相同，继续处理当前分量
│   │       │   ├─→ MMRNNCell (第3层) - 结构与第1层相同，继续处理当前分量
│   │       │   └─→ MMRNNCell (第4层) - 结构与第1层相同，继续处理当前分量
│   │       │       # 每层都会接收上一层的输出和更新后的隐藏状态
│   │       │
│   │       └── 🔥分量间状态传递流程🔥:
│   │           ├── 高频分量1处理完成: 输出[B,64,256], 隐藏状态(H1,C1)[B,64,256]
│   │           ├── 高频分量2开始处理:
│   │           │   ├── 隐藏状态适配: (H1,C1)[B,64,256] -> 适配器Linear(64,32) -> (H1',C1')[B,32,256]
│   │           │   └── 处理完成: 输出[B,32,256], 隐藏状态(H2,C2)[B,32,256]
│   │           └── 低频分量开始处理:
│   │               ├── 隐藏状态直接使用: (H2,C2)[B,32,256] (长度已匹配)
│   │               └── 处理完成: 输出[B,32,256], 最终隐藏状态(H3,C3)[B,32,256]
│   │       │
│   │
│   ├── 收集所有分量输出: outputs = [高频1_out, 高频2_out, 低频_out]
│   │                    # 每个输出的维度分别为: [B,64,256], [B,32,256], [B,32,256]
│   │                    # 共num_components=3个分量
│   │                    # 🔥关键: 不同分量保持各自的序列长度特性🔥
│   │
│   ├── 取最后一个分量(低频): [B, 32, 256]      # 提取最后一个分量(低频)的特征
│   │                                          # 32为低频分量的序列长度，256为hidden_dim
│   │                                          # 低频分量已融合了来自高频分量的信息
│   │
│   ├── 平均池化: [B, 32, 256] -> [B, 256]     # 在序列维度上进行平均池化
│   │                                        # 输出维度256由hidden_dim决定
│   │
│   ├── 🔥分类头处理 (layernorm-fc-relu-drop-fc结构)🔥:
│   │   ├── LayerNorm: [B, 256] -> [B, 256]           # 特征归一化，稳定训练
│   │   ├── Linear1: [B, 256] -> [B, 512]             # 第一个全连接层，扩展到512维
│   │   ├── ReLU: [B, 512] -> [B, 512]                # ReLU激活，增加非线性
│   │   ├── Dropout: [B, 512] -> [B, 512]             # Dropout正则化，防止过拟合
│   │   └── Linear2: [B, 512] -> [B, num_classes]     # 第二个全连接层，映射到类别数
│   │                                                 # num_classes根据数据集确定(11/24/25/26)
│
└── 最终输出: [B, num_classes]                  # 网络输出, 每类的预测分数(logits)
                                            # num_classes根据数据集确定：
                                            # - RML: 11类, RML2018.01a: 24类
                                            # - HisarMod: 26类, TorchSig: 25类

🔥核心创新总结🔥:
├── 隐藏状态长度自适应: 通过动态线性适配器处理不同序列长度的分量间状态传递
├── 分量保持架构: 各分量保持原始时频特性，避免信息丢失
├── 跨频率域信息融合: 高频->低频的信息流动，增强特征表达
├── 学习型小波滤波器: 神经网络学习优化的小波分解，替代固定滤波器
└── 🔥统一增强分类器🔥: layernorm-fc-relu-drop-fc结构，统一512维中间层，增强非线性表达和正则化能力
```

## 2. 参数传递详解与数据流

### 2.1 输入数据流与初始处理

输入IQ信号的维度为[B, 2, 128]，其中B是批次大小，2表示I/Q两个通道，128是序列长度（RML数据集）。通道数由`in_channels=2`参数决定。

```python
# 初始卷积层处理步骤
x = self._process_initial_features(x)  # [B, 2, 128] -> [B, 64, 128]
```

数据首先通过一系列卷积层处理：
1. 输入预处理: [B, 2, 128] -> [B, 1, 2, 128]，添加通道维度用于2D卷积
   ```python
   # 如有必要，添加通道维度: [B, 2, L] -> [B, 1, 2, L]
   if x.dim() == 3:
       x = x.unsqueeze(1)
   ```

2. 2D卷积 `conv1`: [B, 1, 2, 128] -> [B, 64, 1, 128]，处理I/Q通道
   ```python
   # 从AWN模型添加的初始卷积层
   # 1. 2D卷积，处理IQ通道
   self.conv1 = nn.Conv2d(1, 64, kernel_size=(2, 7), padding=(0, 3))
   self.bn1 = nn.BatchNorm2d(64)
   ```
   此处的2D卷积使用kernel_size=(2,7)，特别设计用于处理IQ信号的两个通道，同时捕获时间域的局部特征。

3. 维度调整: [B, 64, 1, 128] -> [B, 64, 128]，去除多余维度
   ```python
   # 去除多余维度: [B, 64, 1, L] -> [B, 64, L]
   x = x.squeeze(2)
   ```

4. 1D卷积 `conv2`: [B, 64, 128] -> [B, 64, 128]，使用5×1卷积核提取局部特征
   ```python
   # 2. 1D卷积序列
   self.conv2 = nn.Conv1d(64, 64, kernel_size=5, padding=2)
   self.bn2 = nn.BatchNorm1d(64)
   ```
   kernel_size=5的卷积提供了较大的感受野，可以捕获更宽范围的时间特征。

5. 1D卷积 `conv3`: [B, 64, 128] -> [B, 64, 128]，使用3×1卷积核进一步提取特征
   ```python
   self.conv3 = nn.Conv1d(64, 64, kernel_size=3, padding=1)
   self.bn3 = nn.BatchNorm1d(64)
   ```
   kernel_size=3的卷积进一步细化特征提取，关注更细粒度的局部模式。

每个卷积层后都应用了批归一化(BatchNorm)和ReLU激活函数，增强特征表达能力并提高训练稳定性。

### 2.2 小波分解与特征提取

```python
# 小波分解与特征提取
high_features_list, low_features = self._process_input(x)  # [B, 64, 128] -> 多个分量
```

小波分解是一种多分辨率分析方法，可以将信号分解为不同频率范围的分量。在设置`num_levels=2`的情况下，执行二级小波分解，产生以下分量：

**实际的小波分解过程（基于提升方案）：**

1. **第1级分解**：
   - 输入: [B, 64, 128]
   - 分裂: 偶数索引[B, 64, 64] + 奇数索引[B, 64, 64]
   - 更新: 低频1 = 偶数 + U1(奇数) -> [B, 64, 64]
   - 预测: 高频1 = 奇数 - P1(低频1) -> [B, 64, 64]

2. **第2级分解**：
   - 输入: 低频1 [B, 64, 64]
   - 分裂: 偶数索引[B, 64, 32] + 奇数索引[B, 64, 32]
   - 更新: 低频2 = 偶数 + U2(奇数) -> [B, 64, 32]
   - 预测: 高频2 = 奇数 - P2(低频2) -> [B, 64, 32]

**分解结果**：
- 高频分量1: [B, 64, 64] - 第一级高频分量，捕获最细粒度的高频变化
- 高频分量2: [B, 64, 32] - 第二级高频分量，捕获中等尺度的频率特征
- 低频分量: [B, 64, 32] - 最终低频分量，包含信号的整体特征和长期趋势

高频分量数量等于`num_levels=2`，总分量数量等于`num_levels+1=3`。

**提升方案的实现**：

本项目中的小波分解使用了提升方案(Lifting Scheme)，它是一种快速计算小波变换的方法，通过预测和更新操作实现：

```python
class WaveletDecomposition(nn.Module):
    def __init__(self, channels, decomposition_levels=2):
        super(WaveletDecomposition, self).__init__()
        self.decomposition_levels = decomposition_levels

        # 创建预测算子模块列表，每个级别一个
        self.predictors = nn.ModuleList([
            PredictUpdateOperator(channels) for _ in range(decomposition_levels)
        ])

        # 创建更新算子模块列表，每个级别一个
        self.updaters = nn.ModuleList([
            PredictUpdateOperator(channels) for _ in range(decomposition_levels)
        ])
```

每个`PredictUpdateOperator`使用双向LSTM学习优化的小波滤波器，取代了传统固定滤波器的小波变换：

```python
class PredictUpdateOperator(nn.Module):
    def __init__(self, channels):
        super(PredictUpdateOperator, self).__init__()
        # 使用双向LSTM处理序列数据
        self.lstm = nn.LSTM(
            input_size=channels,
            hidden_size=channels,
            num_layers=1,
            batch_first=False,
            bidirectional=True
        )
        # 线性层将双向LSTM的输出映射回原始通道数
        self.linear = nn.Linear(channels * 2, channels)
        # Tanh激活函数用于限制输出范围在[-1,1]
        self.tanh = nn.Tanh()
```

**特征提取阶段**：

每个分量通过独立的卷积层处理，通道数从64变为`wavelet_dim=256`（RML数据集配置）：

```python
# 对低频分量应用卷积特征提取
low_features = self.conv_low(low)  # [B, 64, 32] -> [B, 256, 32]

# 对每个高频分量应用卷积特征提取
high_features_list = []
for i, high in enumerate(high_list):
    high_features = self.conv_high_list[i](high)  # [B, 64, seq_len] -> [B, 256, seq_len]
    high_features_list.append(high_features)
```

### 2.3 MMRNNClassifier的分量处理机制

MMRNNClassifier采用了新的分量列表处理方式，能够处理不同序列长度的分量：

```python
def forward(self, components_list, component_lengths=None):
    # components_list: [高频1[B,64,256], 高频2[B,32,256], 低频[B,32,256]]
    outputs = []
    layer_hidden_states = [None] * self.num_layers  # 4层MMRNNCell的隐藏状态

    # 逐分量处理序列（高频1→高频2→低频）
    for c, component in enumerate(components_list):
        B, S_c, W = component.shape  # S_c为当前分量的序列长度

        # 将当前分量的特征投影到隐藏维度
        component_proj = self.input_proj(component)  # [B, S_c, 256] -> [B, S_c, 256]

        # 通过每一层MMRNNCell处理当前分量
        for layer_idx, layer in enumerate(self.rnn_layers):
            if layer_idx == 0:
                layer_input = component_proj
            else:
                layer_input = out

            # MMRNNCell会自动适配隐藏状态维度
            out, new_hidden = layer(layer_input, layer_hidden_states[layer_idx])
            layer_hidden_states[layer_idx] = new_hidden

        outputs.append(out)
```

### 2.4 MMRNNCell的状态更新机制

MMRNNCell使用类似LSTM的机制，但使用Mamba处理序列信息：

```python
def forward(self, xt, hidden_states):
    B, S, C = xt.shape

    # 隐藏状态自适应调整
    if hidden_states is None:
        hx = torch.zeros(B, S, C, device=xt.device)
        cx = torch.zeros(B, S, C, device=xt.device)
    else:
        hx, cx = hidden_states
        # 自适应调整隐藏状态的序列长度以匹配当前输入
        hx = self._adapt_hidden_state(hx, S)
        cx = self._adapt_hidden_state(cx, S)

    # 通过MSB层处理
    for index, layer in enumerate(self.MSBs):
        if index == 0:
            x = layer(xt, hx)  # 第一层接收输入和隐藏状态
        else:
            x = layer(outputs[-1], None)  # 后续层仅使用前一层输出

    o_t = outputs[-1]

    # 门控机制
    Ft = torch.sigmoid(o_t)      # 门控系数
    cell = torch.tanh(o_t)       # 候选单元状态
    Ct = Ft * (cx + cell)        # 更新单元状态
    Ht = Ft * torch.tanh(Ct)     # 计算新的隐藏状态

    return Ht, (Ht, Ct)
```

**关键特性**：
1. **隐藏状态自适应**：通过线性投影自动调整不同序列长度间的隐藏状态传递
2. **门控机制**：结合LSTM的记忆能力和Mamba的序列建模能力
3. **跨分量信息传递**：信息从高频分量流向低频分量

### 2.5 Mamba模块处理

Mamba是状态空间模型(SSM)的高效实现，MSB类封装了Mamba的功能：

```python
class MSB(MSSBlock):
    def forward(self, x, hx=None):
        B, T, C = x.shape
        shortcut = x
        x = self.ln_1(x)  # 层归一化

        if hx is not None:  # 隐藏状态融合
            hx = self.ln_1(hx)
            x = torch.cat((x, hx), dim=-1)  # [B, T, C*2]
            x = self.linear(x)              # [B, T, C*2] -> [B, T, C]

        mamba_out = self.self_attention(x)  # Mamba处理
        x = self.dropout(mamba_out)
        x = shortcut + x  # 残差连接

        return x
```

**Mamba的优势**：
- 线性复杂度O(n)，相比Transformer的O(n²)更高效
- 选择性记忆机制，能够保留重要信息
- 适合处理长序列数据

### 2.6 最终分类过程

```python
# 取最后一个分量的输出（低频分量）
final_output = outputs[-1]  # [B, 32, 256]

# 平均池化
pooled_output = torch.mean(final_output, dim=1)  # [B, 256]

# 🔥新的分类头 (norm-fc-drop-relu-fc结构)🔥
logits = self.classifier(pooled_output)  # [B, num_classes]
```

**🔥增强的分类流程🔥**：
1. **选择低频分量**：低频分量包含整体信号特征，且已融合了高频信息
2. **平均池化**：在序列维度上融合时间信息
3. **增强分类器**：norm-fc-drop-relu-fc五层结构，显著提升分类能力

### 2.7 新的分类器结构详解

新的分类器采用layernorm-fc-relu-drop-fc结构，相比原来的简单LayerNorm+Linear结构有显著改进：

```python
self.classifier = nn.Sequential(
    nn.LayerNorm(hidden_dim),                    # 特征归一化，稳定训练
    nn.Linear(hidden_dim, 512),                  # 第一个全连接层，扩展到512维
    nn.ReLU(inplace=True),                       # ReLU激活，增加非线性
    nn.Dropout(dropout),                         # Dropout正则化，防止过拟合
    nn.Linear(512, num_classes)                  # 第二个全连接层，映射到类别数
)
```

**统一的中间维度策略**：
- 第一个线性层：`hidden_dim -> 512` (统一扩展到512维)
- 第二个线性层：`512 -> num_classes` (映射到类别数)

**各数据集的维度变化**：
- **RML**: [B, 256] -> [B, 512] -> [B, 11] (扩展2倍)
- **RML2018.01a**: [B, 128] -> [B, 512] -> [B, 24] (扩展4倍)
- **HisarMod**: [B, 256] -> [B, 512] -> [B, 26] (扩展2倍)
- **TorchSig**: [B, 128] -> [B, 512] -> [B, 25] (扩展4倍)

**新分类器的优势**：
1. **统一中间维度**：所有数据集都使用512维中间层，简化配置管理
2. **非线性增强**：ReLU激活函数增加非线性表达能力，提升分类性能
3. **正则化强化**：Dropout层有效防止过拟合，提高泛化能力
4. **训练稳定性**：LayerNorm归一化稳定特征分布，改善训练过程
5. **容量适配**：512维中间层为不同复杂度数据集提供充足的表达容量

## 3. 不同数据集的参数配置分析

### 3.1 基于config.yaml的数据集特定配置

根据实际的配置文件，不同数据集使用了不同的参数组合：

| 数据集 | 序列长度 | num_classes | wavelet_dim | rnn_dim | num_layers | num_levels | 分量数 | 分类器维度变化 | 特点 |
|--------|----------|-------------|-------------|---------|------------|------------|--------|----------------|------|
| RML | 128 | 11 | 256 | 256 | 4 | 2 | 3 | [256]→[512]→[11] | 短序列，大特征维度，扩展2倍 |
| RML2018.01a | 1024 | 24 | 64 | 128 | 3 | 2 | 3 | [128]→[512]→[24] | 长序列，中等特征维度，扩展4倍 |
| HisarMod | 1024 | 26 | 32 | 256 | 4 | 2 | 3 | [256]→[512]→[26] | 长序列，小wavelet_dim，大rnn_dim，扩展2倍 |
| TorchSig1024 | 1024 | 25 | 64 | 128 | 3 | 2 | 3 | [128]→[512]→[25] | 长序列，中等特征维度，扩展4倍 |
| TorchSig2048 | 2048 | 25 | 64 | 128 | 4 | 1 | 2 | [128]→[512]→[25] | 超长序列，减少分解级数，扩展4倍 |
| TorchSig4096 | 4096 | 25 | 256 | 128 | 3 | 1 | 2 | [128]→[512]→[25] | 最长序列，大wavelet_dim，减少分解级数，扩展4倍 |

### 3.2 num_levels=2的模型特性（主流配置）

大多数数据集采用`num_levels=2`配置，具有以下特性：

1. **分量数量与序列长度变化**：
   - 产生2个高频分量和1个低频分量，总共3个分量
   - `num_components = num_levels + 1 = 2 + 1 = 3`
   - 以RML数据集为例：
     - 原始序列长度：128
     - 高频分量1：64（128/2）
     - 高频分量2：32（64/2）
     - 低频分量：32（与高频分量2相同）

2. **频率分析能力**：
   - 高频分量1：捕获最细粒度的高频变化（如相位跳变、符号边界）
   - 高频分量2：捕获中等尺度的频率特征（如符号速率相关特征）
   - 低频分量：捕获信号的整体趋势（如载波特性、能量分布）

3. **信息传递路径**：
   - 信息流向：高频1 → 高频2 → 低频
   - 通过隐藏状态自适应机制处理不同序列长度
   - 分量间状态传递增强了频谱域的特征融合

### 3.3 长序列数据集的特殊配置

对于超长序列（TorchSig2048/4096），采用`num_levels=1`：

1. **设计考虑**：
   - 减少分解级数以控制计算复杂度
   - 避免过度分解导致的信息丢失
   - 平衡模型性能和计算效率

2. **分量特性**：
   - 仅产生1个高频分量和1个低频分量
   - 高频分量：序列长度减半
   - 低频分量：与高频分量长度相同

### 3.4 参数协同关系分析

1. **wavelet_dim与序列长度的关系**：
   - 短序列（128）：使用大特征维度（256）补偿信息密度
   - 长序列（1024+）：使用较小特征维度（32-128）避免过拟合

2. **rnn_dim与num_layers的平衡**：
   - 高rnn_dim + 少层数：适合复杂特征表示
   - 低rnn_dim + 多层数：适合深层特征抽象

3. **num_levels与序列长度的适配**：
   - 短序列：可以使用更多分解级数（num_levels=2）
   - 超长序列：减少分解级数（num_levels=1）避免过度分解

## 4. 关键技术创新点

### 4.1 隐藏状态自适应机制

WNN-MRNN的一个重要创新是隐藏状态自适应机制，能够处理不同序列长度的分量：

```python
def _adapt_hidden_state(self, hidden_state, target_length):
    """自适应调整隐藏状态的序列长度"""
    if hidden_state is None:
        return None

    B, S_source, C = hidden_state.shape
    if S_source == target_length:
        return hidden_state

    # 动态创建适配器进行序列长度转换
    adapter = self._get_or_create_adapter(S_source, target_length, device=hidden_state.device)

    # 转置 -> 投影 -> 转置回来
    hidden_state = hidden_state.transpose(1, 2)  # [B, S, C] -> [B, C, S]
    hidden_state = adapter(hidden_state)         # [B, C, S_source] -> [B, C, S_target]
    hidden_state = hidden_state.transpose(1, 2)  # [B, C, S] -> [B, S, C]

    return hidden_state
```

### 4.2 提升方案的神经网络实现

使用双向LSTM学习优化的小波滤波器，替代传统固定滤波器：

```python
class PredictUpdateOperator(nn.Module):
    def __init__(self, channels):
        super(PredictUpdateOperator, self).__init__()
        self.lstm = nn.LSTM(
            input_size=channels,
            hidden_size=channels,
            num_layers=1,
            batch_first=False,
            bidirectional=True
        )
        self.linear = nn.Linear(channels * 2, channels)
        self.tanh = nn.Tanh()
```

### 4.3 分量列表处理架构

新的分量处理方式保持各分量的原始维度特性：

```python
def _prepare_wavelet_components_for_mrnn(self, high_features_list, low_features):
    """准备小波分量用于MRNN处理，保持各分量的原始维度"""
    components_list = []
    component_lengths = []

    # 处理所有高频分量，保持原始维度
    for high_features in high_features_list:
        high_features = high_features.permute(0, 2, 1)  # [B, C, S] -> [B, S, C]
        components_list.append(high_features)
        component_lengths.append(high_features.size(1))

    # 处理低频分量
    low_features = low_features.permute(0, 2, 1)
    components_list.append(low_features)
    component_lengths.append(low_features.size(1))

    return components_list, component_lengths
```

## 5. 结论

WNN-MRNN模型通过结合小波分解和基于Mamba的递归网络，实现了对调制信号的高效分析和分类。其关键优势包括：

1. **多分辨率分析**：通过提升方案的小波分解捕获不同频率范围的信号特征，为复杂调制信号提供全面的时频域表示。

2. **自适应状态传递**：创新的隐藏状态自适应机制能够处理不同序列长度的分量，实现跨频率域的信息融合。

3. **高效序列处理**：利用Mamba状态空间模型代替传统RNN/LSTM，提供线性复杂度的序列建模能力。

4. **灵活的参数配置**：针对不同数据集特性（序列长度、复杂度）提供了优化的参数组合，平衡性能和计算效率。

5. **学习型小波滤波器**：使用神经网络学习优化的小波滤波器，相比传统固定滤波器具有更强的适应性。

6. **分量保持架构**：新的分量列表处理方式保持各分量的原始时频特性，避免信息丢失。

本文档基于实际代码分析，详细描述了WNN-MRNN的参数传递流程和数据维度变化，为模型的理解、调优和应用提供了准确的技术参考。该架构在调制识别任务中展现了强大的潜力，特别是在处理不同长度序列和复杂信号环境时的鲁棒性。
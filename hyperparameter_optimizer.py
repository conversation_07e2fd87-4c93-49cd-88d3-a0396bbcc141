#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WNN-MRNN 超参数优化器
专门优化 wavelet_dim, num_levels, rnn_dim, num_layers, dropout, batch_size 六个关键参数
支持显存不足时跳过实验，支持自定义参数选择
"""

# ==================== 配置区域 ====================
# 在这里统一配置所有优化参数，方便修改

# 基本配置
OPTIMIZATION_CONFIG = {
    # 实验基本设置
    'study_name': 'wnn_mrnn_hyperopt',
    'config_path': 'config.yaml',

    # 要优化的数据集列表
    'datasets': ['rml201801a'],

    # 优化模式
    'optimization_mode': 'separate',  # 'separate': 分别优化每个数据集, 'combined': 联合优

    # 训练配置
    'training_epochs': 200,  # 优化时使用的训练轮数
    'early_stop_patience': 15,  # 早停耐心值
    'min_epochs': 2,  # 最少训练轮数，避免过早停止
    'n_trials_per_dataset': 1,  # 每个数据集的最少试验次数（实际会继续直到有完整训练完成）

    # 每个数据集的第2轮初始门槛值（基于历史经验设置）
    'initial_second_epoch_thresholds': {
        'rml': 61.0,  # RML数据集第2轮的基准门槛
        'rml201801a': 60,  # RML2018.01a数据集第2轮的基准门槛
        'hisar': 55.0,  # HISAR数据集第2轮的基准门槛
        'torchsig1024': 53.5,  # TorchSig1024数据集第2轮的基准门槛
        'torchsig2048': 55.5,  # TorchSig2048数据集第2轮的基准门槛
        'torchsig4096': 59.5,  # TorchSig4096数据集第2轮的基准门槛
    },

    # 参数优化配置
    #'optimize_params': ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers', 'dropout', 'batch_size', 'lambda_lifting'],  # 要优化的参数列表，可自定义选择
    'optimize_params': ['dropout', 'lambda_lifting'],
    # 要优化的参数列表，可自定义选择
    'skip_on_oom': True,  # 是否在显存不足时跳过实验
    'max_oom_retries': 1,  # 显存不足时的最大重试次数

    # 存储配置
    'save_frequency': 1,  # 每N次试验保存一次结
    'enable_database': True,  # 是否启用数据库存储（支持断点续传）
    'database_url': 'sqlite:///wnn_mrnn_optimization.db',  # 数据库路径
}

# 每个数据集的参数搜索范围
PARAMETER_RANGES = {
    'rml': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [32, 64, 128, 256],
        'lambda_lifting': (0.001, 0.02)  # 小波提升损失权重范围
    },
    'rml201801a': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.1, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [32, 64, 128, 256],
        'lambda_lifting': (0.1, 0.5)  # 小波提升损失权重范围
    },
    'hisar': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [32, 64, 128, 256],
        'lambda_lifting': (0.001, 0.02)  # 小波提升损失权重范围
    },
    'torchsig1024': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [32, 64, 128, 256],
        'lambda_lifting': (0.001, 0.02)  # 小波提升损失权重范围
    },
    'torchsig2048': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [16, 32, 64, 128],  # 较大序列长度使用较小batch_size
        'lambda_lifting': (0.001, 0.02)  # 小波提升损失权重范围
    },
    'torchsig4096': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [8, 16, 32, 64],  # 最大序列长度使用最小batch_size
        'lambda_lifting': (0.001, 0.02)  # 小波提升损失权重范围
    }
}

# ==================== 代码实现区域 ====================

import os
import sys
import yaml
import copy
import optuna
import logging
import json
import time
import torch
import gc
from typing import Dict, List, Tuple
import tempfile
import shutil
from datetime import datetime

# 添加当前目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train import main as train_main

def clear_gpu_memory():
    """清理GPU显存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def check_gpu_memory():
    """检查GPU显存使用情况"""
    if torch.cuda.is_available():
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        allocated_memory = torch.cuda.memory_allocated(0) / 1024**3  # GB
        cached_memory = torch.cuda.memory_reserved(0) / 1024**3  # GB
        free_memory = total_memory - cached_memory
        return {
            'total': total_memory,
            'allocated': allocated_memory,
            'cached': cached_memory,
            'free': free_memory
        }
    return None

def is_oom_error(exception):
    """判断是否为显存不足错误"""
    error_msg = str(exception).lower()
    oom_keywords = [
        'out of memory',
        'cuda out of memory',
        'runtime error',
        'memory error',
        'allocation failed'
    ]
    return any(keyword in error_msg for keyword in oom_keywords)






class WNNMRNNSeparateOptimizer:
    """WNN-MRNN分别优化器 - 为每个数据集单独寻找最优参数"""

    def __init__(self, config=None):
        """初始化分别优化器"""
        self.config = config or OPTIMIZATION_CONFIG
        self.config_path = self.config['config_path']
        self.datasets = self.config['datasets']
        self.n_trials_per_dataset = self.config['n_trials_per_dataset']
        self.study_name = self.config['study_name']
        self.save_frequency = self.config['save_frequency']

        # 加载基础配置
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.base_config = yaml.safe_load(f)

        # 创建结果保存目录
        self.results_dir = f'optimization_results_{self.study_name}_separate'
        os.makedirs(self.results_dir, exist_ok=True)

        # 设置日志
        log_file = os.path.join(self.results_dir, f'{self.study_name}_separate_optimization.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        # 为每个数据集初始化结果跟踪
        self.dataset_results = {}
        self.all_results = {}  # 按数据集分组的所有结果
        self.second_epoch_threshold = {}  # 每个数据集第2轮的门槛

        for dataset in self.datasets:
            self.dataset_results[dataset] = {
                'best_result': None,
                'all_trials': [],
                'best_file': os.path.join(self.results_dir, f'{dataset}_best_results.json'),
                'all_file': os.path.join(self.results_dir, f'{dataset}_all_results.json'),
                'ranked_file': os.path.join(self.results_dir, f'{dataset}_ranked_results.json')
            }
            self.all_results[dataset] = []
            # 使用配置中的初始门槛值，如果没有配置则使用0.0
            initial_thresholds = self.config.get('initial_second_epoch_thresholds', {})
            self.second_epoch_threshold[dataset] = initial_thresholds.get(dataset, 0.0)
            if self.second_epoch_threshold[dataset] > 0:
                self.logger.info(f"🎯 {dataset} 使用初始第2轮门槛: {self.second_epoch_threshold[dataset]:.4f}")
            self.load_existing_results_for_dataset(dataset)

    def load_existing_results_for_dataset(self, dataset):
        """加载指定数据集的已有结果"""
        dataset_info = self.dataset_results[dataset]

        # 加载最佳结果
        if os.path.exists(dataset_info['best_file']):
            try:
                with open(dataset_info['best_file'], 'r', encoding='utf-8') as f:
                    dataset_info['best_result'] = json.load(f)
                self.logger.info(f"加载 {dataset} 已有最佳结果: 准确率 {dataset_info['best_result']['best_value']:.4f}")
            except Exception as e:
                self.logger.warning(f"加载 {dataset} 最佳结果失败: {e}")

        # 加载所有结果
        if os.path.exists(dataset_info['all_file']):
            try:
                with open(dataset_info['all_file'], 'r', encoding='utf-8') as f:
                    dataset_info['all_trials'] = json.load(f)
                    self.all_results[dataset] = dataset_info['all_trials']
                self.logger.info(f"加载 {dataset} 已有试验结果: {len(dataset_info['all_trials'])} 次试验")
            except Exception as e:
                self.logger.warning(f"加载 {dataset} 试验历史失败: {e}")

    def save_dataset_results(self, dataset, trial_number, params, value, result=None):
        """保存指定数据集的结果"""
        current_time = datetime.now().isoformat()
        dataset_info = self.dataset_results[dataset]

        # 保存当前试验结果
        trial_result = {
            'trial_number': trial_number,
            'timestamp': current_time,
            'parameters': params,
            'value': value,
            'dataset': dataset,
            'result': result  # 保存完整的训练结果
        }

        dataset_info['all_trials'].append(trial_result)
        self.all_results[dataset].append(trial_result)

        # 更新最佳结果
        if dataset_info['best_result'] is None or value > dataset_info['best_result']['best_value']:
            dataset_info['best_result'] = {
                'trial_number': trial_number,
                'timestamp': current_time,
                'best_params': params,
                'best_value': value,
                'dataset': dataset,
                'total_trials_so_far': len(dataset_info['all_trials'])
            }

            # 保存最佳结果
            with open(dataset_info['best_file'], 'w', encoding='utf-8') as f:
                json.dump(dataset_info['best_result'], f, indent=2, ensure_ascii=False)

            self.logger.info(f"🎉 {dataset} 发现新的最佳结果! 试验 {trial_number}, 准确率: {value:.4f}")
            self.logger.info(f"{dataset} 最佳参数: {params}")

        # 保存排序结果
        self.save_ranked_results_for_dataset(dataset)

        # 定期保存所有结果
        if trial_number % self.save_frequency == 0:
            with open(dataset_info['all_file'], 'w', encoding='utf-8') as f:
                json.dump(dataset_info['all_trials'], f, indent=2, ensure_ascii=False)

    def save_ranked_results_for_dataset(self, dataset):
        """保存指定数据集的排序结果"""
        dataset_info = self.dataset_results[dataset]
        if not dataset_info['all_trials']:
            return

        # 按准确率降序排序
        sorted_results = sorted(dataset_info['all_trials'], key=lambda x: x.get('value', 0), reverse=True)

        # 添加排名信息
        ranked_results = []
        for rank, result in enumerate(sorted_results, 1):
            ranked_result = result.copy()
            ranked_result['rank'] = rank
            ranked_results.append(ranked_result)

        # 保存排序结果
        try:
            with open(dataset_info['ranked_file'], 'w', encoding='utf-8') as f:
                json.dump(ranked_results, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.warning(f"保存 {dataset} 排序结果失败: {e}")

    def display_current_ranking_for_dataset(self, dataset, top_n=5):
        """显示指定数据集的当前排名"""
        dataset_info = self.dataset_results[dataset]
        if len(dataset_info['all_trials']) < 2:
            return

        # 按准确率排序
        sorted_results = sorted(dataset_info['all_trials'], key=lambda x: x.get('value', 0), reverse=True)

        self.logger.info(f"\n📊 {dataset} 当前TOP {min(top_n, len(sorted_results))} 参数组合排名:")
        self.logger.info("-" * 80)

        for i, result in enumerate(sorted_results[:top_n]):
            rank = i + 1
            trial_num = result.get('trial_number', 'N/A')
            accuracy = result.get('value', 0)
            params = result.get('parameters', {})

            # 格式化参数显示
            param_str = ', '.join([f"{k}={v}" for k, v in params.items()])

            self.logger.info(f"#{rank} | 试验{trial_num} | 准确率:{accuracy:.4f} | {param_str}")

        self.logger.info("-" * 80)

    def _count_completed_trials(self, dataset: str) -> int:
        """
        统计指定数据集中完整训练完成的试验次数
        完整训练完成的标准：early_stopped为False或不存在该字段
        """
        dataset_info = self.dataset_results[dataset]
        completed_count = 0

        for trial in dataset_info['all_trials']:
            # 检查试验结果
            if 'result' in trial and trial['result']:
                result = trial['result']
                # 如果没有early_stopped字段，或者early_stopped为False，则认为是完整训练
                if not result.get('early_stopped', False):
                    completed_count += 1

        return completed_count

    def _get_best_accuracy_from_trials(self, dataset: str) -> float:
        """
        获取指定数据集中所有试验的最优准确率
        用于在没有完整训练完成时，将门槛调整为已有试验的最优值
        返回百分制格式以与门槛格式保持一致

        策略：
        - 如果有完整训练，使用完整训练的最佳准确率
        - 如果只有早停训练，使用早停训练的第二轮准确率
        """
        dataset_info = self.dataset_results[dataset]
        best_accuracy = 0.0
        has_complete_training = False

        # 首先检查是否有完整训练
        for trial in dataset_info['all_trials']:
            if 'result' in trial and trial['result']:
                result = trial['result']
                if not result.get('early_stopped', False):
                    has_complete_training = True
                    break

        for trial in dataset_info['all_trials']:
            if 'result' in trial and trial['result']:
                result = trial['result']
                is_complete_training = not result.get('early_stopped', False)

                if has_complete_training and is_complete_training:
                    # 有完整训练时，只考虑完整训练的最佳准确率
                    accuracy_percent = result['best_val_acc'] * 100.0
                elif not has_complete_training and not is_complete_training:
                    # 没有完整训练时，使用早停训练的第二轮准确率
                    if 'second_epoch_acc' in result and result['second_epoch_acc'] is not None:
                        accuracy_percent = result['second_epoch_acc'] * 100.0
                    else:
                        accuracy_percent = result['best_val_acc'] * 100.0
                else:
                    continue  # 跳过不符合当前策略的试验

                if accuracy_percent > best_accuracy:
                    best_accuracy = accuracy_percent

        return best_accuracy

    def train_with_second_epoch_gate(self, temp_config_path: str, dataset: str, trial_number: int) -> Dict:
        """
        使用第2轮门槛的训练函数
        所有试验都会在第2轮后检查门槛，包括第一次试验
        """
        current_threshold = self.second_epoch_threshold[dataset]
        trial_count = len(self.dataset_results[dataset]['all_trials'])

        self.logger.info(f"🎯 开始训练 {dataset} Trial {trial_number} (第{trial_count + 1}次试验)")
        self.logger.info(f"🚪 当前第2轮门槛: {current_threshold:.4f}")

        # 使用智能训练策略：一次性训练但在第2轮后检查门槛
        import yaml
        with open(temp_config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 设置智能训练参数
        config['training']['second_epoch_threshold'] = current_threshold
        config['training']['enable_second_epoch_gate'] = True
        config['training']['trial_info'] = {
            'dataset': dataset,
            'trial_number': trial_number,
            'trial_count': trial_count + 1
        }

        # 保存修改后的配置
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

        # 运行训练
        original_argv = sys.argv.copy()  # 在try块外定义，确保异常处理中可以访问
        try:
            # 临时修改sys.argv以传递配置文件路径
            sys.argv = ['train.py', '--config', temp_config_path]

            result = train_main()

            if result and 'best_val_acc' in result:
                # 更新门槛（如果这次的第2轮表现更好）
                if 'second_epoch_acc' in result:
                    # 注意：result['second_epoch_acc']是小数制(0.595)，current_threshold是百分制(59.4)
                    # 需要统一格式进行比较
                    second_epoch_acc_percent = result['second_epoch_acc'] * 100.0  # 转换为百分制

                    self.logger.info(f"🔍 {dataset} 第2轮门槛检查:")
                    self.logger.info(f"   - 当前门槛: {current_threshold:.4f}")
                    self.logger.info(f"   - 第2轮准确率: {second_epoch_acc_percent:.4f}")

                    if second_epoch_acc_percent > current_threshold:
                        # 更新门槛为第2轮准确率（保持百分制格式）
                        self.second_epoch_threshold[dataset] = second_epoch_acc_percent
                        self.logger.info(f"🆙 {dataset} 更新第2轮门槛: {current_threshold:.4f} → {second_epoch_acc_percent:.4f}")
                    else:
                        self.logger.info(f"📊 {dataset} 第2轮准确率未超过门槛，门槛保持: {current_threshold:.4f}")

                return result

            else:
                # 训练失败，返回默认结果
                self.logger.warning(f"{dataset} Trial {trial_number} 训练失败")
                return {
                    'best_val_acc': 0.0,
                    'best_val_f1': 0.0,
                    'best_val_kappa': 0.0,
                    'total_epochs': 0,
                    'total_training_time': 0.0,
                    'trainable_parameters': 0,
                    'early_stopped': True
                }

        except Exception as e:
            self.logger.error(f"{dataset} Trial {trial_number} 训练出现异常: {e}")
            return {
                'best_val_acc': 0.0,
                'best_val_f1': 0.0,
                'best_val_kappa': 0.0,
                'total_epochs': 0,
                'total_training_time': 0.0,
                'trainable_parameters': 0,
                'early_stopped': True,
                'error': str(e)
            }
        finally:
            # 确保在任何情况下都恢复原始argv
            sys.argv = original_argv

    def optimize_single_dataset(self, dataset):
        """优化单个数据集"""
        self.logger.info(f"\n🎯 开始优化数据集: {dataset}")
        self.logger.info(f"试验次数: {self.n_trials_per_dataset}")

        # 显示当前最佳结果（如果有）
        dataset_info = self.dataset_results[dataset]
        if dataset_info['best_result']:
            self.logger.info(f"{dataset} 当前最佳结果: 准确率 {dataset_info['best_result']['best_value']:.4f}")
            self.logger.info(f"{dataset} 当前最佳参数: {dataset_info['best_result']['best_params']}")

        # 创建研究
        storage_url = None
        if self.config.get('enable_database', False):
            storage_url = self.config.get('database_url', 'sqlite:///wnn_mrnn_optimization.db')

        study_name = f"{self.study_name}_{dataset}"
        study = optuna.create_study(
            direction='maximize',
            study_name=study_name,
            storage=storage_url,
            load_if_exists=True,
            pruner=optuna.pruners.MedianPruner(n_startup_trials=3, n_warmup_steps=5)
        )

        # 定义目标函数
        def objective(trial):
            # 建议参数
            if dataset not in PARAMETER_RANGES:
                ranges = PARAMETER_RANGES['rml201801a']  # 使用默认范围
            else:
                ranges = PARAMETER_RANGES[dataset]

            # 获取要优化的参数列表
            optimize_params = self.config.get('optimize_params', ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers', 'dropout', 'batch_size'])

            params = {}

            # 根据配置建议参数
            if 'wavelet_dim' in optimize_params:
                params['wavelet_dim'] = trial.suggest_categorical('wavelet_dim', ranges['wavelet_dim'])

            if 'rnn_dim' in optimize_params:
                params['rnn_dim'] = trial.suggest_categorical('rnn_dim', ranges['rnn_dim'])

            if 'num_levels' in optimize_params:
                params['num_levels'] = trial.suggest_categorical('num_levels', ranges['num_levels'])

            if 'num_layers' in optimize_params:
                params['num_layers'] = trial.suggest_categorical('num_layers', ranges['num_layers'])

            if 'dropout' in optimize_params:
                dropout_range = ranges['dropout']
                if isinstance(dropout_range, tuple) and len(dropout_range) == 2:
                    # 连续值范围
                    params['dropout'] = trial.suggest_float('dropout', dropout_range[0], dropout_range[1])
                else:
                    # 离散值列表（向后兼容）
                    params['dropout'] = trial.suggest_categorical('dropout', dropout_range)

            if 'batch_size' in optimize_params:
                params['batch_size'] = trial.suggest_categorical('batch_size', ranges['batch_size'])

            if 'lambda_lifting' in optimize_params:
                lambda_lifting_range = ranges['lambda_lifting']
                if isinstance(lambda_lifting_range, tuple) and len(lambda_lifting_range) == 2:
                    # 连续值范围
                    params['lambda_lifting'] = trial.suggest_float('lambda_lifting', lambda_lifting_range[0], lambda_lifting_range[1])
                else:
                    # 离散值列表（向后兼容）
                    params['lambda_lifting'] = trial.suggest_categorical('lambda_lifting', lambda_lifting_range)

            # 创建临时配置文件
            temp_config_path = self.create_config_with_params(dataset, params)

            self.logger.info(f"{dataset} Trial {trial.number}: 参数 {params}")

            try:
                # 显存不足处理
                oom_retry_count = 0
                max_retries = self.config.get('max_oom_retries', 3)
                skip_on_oom = self.config.get('skip_on_oom', True)

                while oom_retry_count <= max_retries:
                    try:
                        # 清理显存
                        clear_gpu_memory()

                        # 检查显存状态
                        memory_info = check_gpu_memory()
                        if memory_info:
                            self.logger.info(f"显存状态 - 总计: {memory_info['total']:.1f}GB, "
                                           f"已用: {memory_info['allocated']:.1f}GB, "
                                           f"缓存: {memory_info['cached']:.1f}GB, "
                                           f"可用: {memory_info['free']:.1f}GB")

                        # 打印训练前的关键信息
                        self.print_training_info_for_dataset(trial, params, dataset)

                        # 使用第2轮门槛检查进行训练
                        result = self.train_with_second_epoch_gate(temp_config_path, dataset, trial.number)
                        self.logger.info(f"训练完成 {dataset} Trial {trial.number}, 结果: {result}")

                        if result and 'best_val_acc' in result:
                            # 判断是否为完整训练
                            is_complete_training = not result.get('early_stopped', False)

                            if is_complete_training:
                                # 完整训练：使用最佳准确率
                                accuracy = result['best_val_acc']
                                self.logger.info(f"{dataset} Trial {trial.number} 完整训练，使用最佳准确率: {accuracy:.4f}")
                            else:
                                # 早停训练：使用第二轮准确率（如果有的话）
                                if 'second_epoch_acc' in result and result['second_epoch_acc'] is not None:
                                    accuracy = result['second_epoch_acc']
                                    self.logger.info(f"{dataset} Trial {trial.number} 早停训练，使用第2轮准确率: {accuracy:.4f}")
                                else:
                                    accuracy = result['best_val_acc']
                                    self.logger.info(f"{dataset} Trial {trial.number} 早停训练，使用最佳准确率: {accuracy:.4f}")

                            # 保存结果（包含完整的训练结果）
                            self.save_dataset_results(dataset, trial.number, params, accuracy, result)

                            # 显示当前排名
                            if len(dataset_info['all_trials']) >= 2:
                                self.display_current_ranking_for_dataset(dataset, top_n=3)

                            return accuracy
                        else:
                            self.logger.warning(f"{dataset} Trial {trial.number} 训练失败")
                            return 0.0

                        # 训练成功，跳出重试循环
                        break

                    except Exception as e:
                        if is_oom_error(e) and skip_on_oom:
                            oom_retry_count += 1
                            self.logger.warning(f"{dataset} Trial {trial.number} 显存不足 (重试 {oom_retry_count}/{max_retries}): {str(e)}")

                            # 清理显存
                            clear_gpu_memory()

                            if oom_retry_count > max_retries:
                                self.logger.error(f"{dataset} Trial {trial.number} 显存不足，已达到最大重试次数，跳过此参数组合")
                                return 0.0
                            else:
                                # 等待一段时间再重试
                                time.sleep(2)
                                continue
                        else:
                            # 非显存错误，直接返回
                            self.logger.error(f"{dataset} Trial {trial.number} 出现错误: {e}")
                            return 0.0

                    finally:
                        # 清理显存
                        clear_gpu_memory()

                return 0.0

            finally:
                # 确保清理临时文件
                if temp_config_path and os.path.exists(temp_config_path):
                    try:
                        os.unlink(temp_config_path)
                        self.logger.debug(f"已清理临时配置文件: {temp_config_path}")
                    except Exception as e:
                        self.logger.warning(f"清理临时文件失败: {e}")

        # 阶段1：执行初始试验
        self.logger.info(f"🚀 阶段1: 进行前{self.n_trials_per_dataset}轮试验")
        phase1_trials_completed = 0
        try:
            study.optimize(objective, n_trials=self.n_trials_per_dataset)
            phase1_trials_completed = len(study.trials)
        except KeyboardInterrupt:
            self.logger.info(f"{dataset} 优化被用户中断，保存当前结果...")
            phase1_trials_completed = len(study.trials)
        except Exception as e:
            self.logger.error(f"{dataset} 优化过程中出现错误: {e}")
            phase1_trials_completed = len(study.trials)

        # 检查实际完成的试验次数和完整训练完成的试验次数
        completed_trials = self._count_completed_trials(dataset)
        total_trials_in_dataset = len(self.dataset_results[dataset]['all_trials'])

        self.logger.info(f"✅ 阶段1完成:")
        self.logger.info(f"   - 计划试验次数: {self.n_trials_per_dataset}")
        self.logger.info(f"   - 实际完成试验次数: {total_trials_in_dataset}")
        self.logger.info(f"   - 完整训练完成次数: {completed_trials}")

        # 阶段2：只有在完成规定次数且没有完整训练完成时才调整门槛
        if total_trials_in_dataset >= self.n_trials_per_dataset and completed_trials == 0:
            self.logger.info(f"⚠️ 阶段2: 前{total_trials_in_dataset}轮试验没有完整训练完成，自动调整门槛")

            # 获取前n轮的最优准确率作为新门槛
            best_acc_in_phase1 = self._get_best_accuracy_from_trials(dataset)
            original_threshold = self.second_epoch_threshold[dataset]

            self.logger.info(f"📊 前{total_trials_in_dataset}轮试验分析:")
            self.logger.info(f"   - 原始门槛: {original_threshold:.4f}")
            self.logger.info(f"   - 前{total_trials_in_dataset}轮最优准确率: {best_acc_in_phase1:.4f}")

            if best_acc_in_phase1 > 0:
                # 更新门槛为前n轮的最优值
                self.second_epoch_threshold[dataset] = best_acc_in_phase1
                self.logger.info(f"🔧 {dataset} 门槛自动调整: {original_threshold:.4f} → {best_acc_in_phase1:.4f}")
                self.logger.info(f"📊 新策略: 使用前{total_trials_in_dataset}轮最优准确率作为门槛，继续试验直到完整训练完成")

                # 显示门槛调整的合理性
                if best_acc_in_phase1 < original_threshold:
                    self.logger.info(f"✅ 门槛降低合理，从 {original_threshold:.4f} 降至 {best_acc_in_phase1:.4f}")
                else:
                    self.logger.info(f"⚠️ 注意：新门槛 {best_acc_in_phase1:.4f} 高于原门槛 {original_threshold:.4f}")
            else:
                self.logger.warning(f"⚠️ 前{total_trials_in_dataset}轮无有效结果，保持原门槛 {original_threshold:.4f}")

            max_additional_trials = 50  # 防止无限循环，最多额外50次试验
            additional_trials = 0

            while completed_trials == 0 and additional_trials < max_additional_trials:
                # 每次进行5次试验，然后检查
                batch_size = 5
                current_threshold = self.second_epoch_threshold[dataset]
                self.logger.info(f"🔄 使用新门槛 {current_threshold:.4f} 进行额外 {batch_size} 次试验 (已额外进行 {additional_trials} 次)")

                try:
                    study.optimize(objective, n_trials=batch_size)
                except KeyboardInterrupt:
                    self.logger.info(f"{dataset} 优化被用户中断...")
                    break
                except Exception as e:
                    self.logger.error(f"{dataset} 额外试验中出现错误: {e}")
                    break

                additional_trials += batch_size

                # 重新检查完整训练完成次数
                completed_trials = self._count_completed_trials(dataset)
                self.logger.info(f"📈 当前已有 {completed_trials} 次完整训练完成")

            if completed_trials > 0:
                final_threshold = self.second_epoch_threshold[dataset]
                self.logger.info(f"🎉 成功！经过 {total_trials_in_dataset + additional_trials} 次试验，有 {completed_trials} 次完整训练完成")
                self.logger.info(f"🏆 最终使用门槛: {final_threshold:.4f} (原始: {original_threshold:.4f})")
            else:
                self.logger.warning(f"⚠️ 达到最大试验次数限制 ({total_trials_in_dataset + max_additional_trials})，仍无完整训练完成")
        else:
            # 规定次数已完成，检查是否有完整训练
            if completed_trials > 0:
                self.logger.info(f"🎉 规定的 {self.n_trials_per_dataset} 次试验已完成，有 {completed_trials} 次完整训练完成，优化结束")
            else:
                self.logger.info(f"🎉 规定的 {self.n_trials_per_dataset} 次试验已完成，但无完整训练完成")

        # 最终保存结果
        with open(dataset_info['all_file'], 'w', encoding='utf-8') as f:
            json.dump(dataset_info['all_trials'], f, indent=2, ensure_ascii=False)

        return dataset_info['best_result']

    def print_training_info_for_dataset(self, trial: optuna.Trial, params: Dict, dataset: str):
        """打印数据集训练前的关键信息"""
        dataset_info = self.dataset_results[dataset]

        self.logger.info("=" * 100)
        self.logger.info(f"🚀 开始训练 - {dataset} Trial {trial.number}")
        self.logger.info("=" * 100)

        # 1. 本次训练的相关参数
        self.logger.info("📋 本次训练参数:")
        param_lines = []
        for key, value in params.items():
            if isinstance(value, float):
                param_lines.append(f"  {key}: {value:.4f}")
            else:
                param_lines.append(f"  {key}: {value}")
        self.logger.info("\n".join(param_lines))

        # 2. 当前数据集的TOP参数（如果有历史记录）
        if len(dataset_info['all_trials']) > 0:
            self.logger.info(f"\n🏆 {dataset} 当前TOP 3最佳参数组合:")
            sorted_results = sorted(dataset_info['all_trials'], key=lambda x: x.get('value', 0), reverse=True)
            top_results = sorted_results[:3]

            for i, result in enumerate(top_results):
                rank = i + 1
                trial_num = result.get('trial_number', 'N/A')
                accuracy = result.get('value', 0)
                result_params = result.get('parameters', {})

                param_str = ', '.join([f"{k}={v:.4f}" if isinstance(v, float) else f"{k}={v}"
                                     for k, v in result_params.items()])
                self.logger.info(f"  #{rank} | Trial{trial_num} | 准确率:{accuracy:.4f} | {param_str}")
        else:
            self.logger.info(f"\n🏆 {dataset} 当前TOP参数: 暂无历史记录")

        # 3. 估算模型参数量（基于参数配置）
        estimated_params = self.estimate_model_parameters_for_dataset(params, dataset)
        self.logger.info(f"\n🔧 预估模型参数量: {estimated_params:,}")

        self.logger.info("=" * 100)
        self.logger.info("🎯 开始训练...")
        self.logger.info("=" * 100)

    def estimate_model_parameters_for_dataset(self, params: Dict, dataset: str) -> int:
        """估算指定数据集的模型参数量"""
        try:
            # 获取数据集信息
            if dataset == 'rml':
                num_classes = len(self.base_config['rml_class_names'])
                sequence_length = self.base_config['data']['sequence_lengths']['rml']
            elif dataset == 'rml201801a':
                num_classes = len(self.base_config['rml201801a_class_names'])
                sequence_length = self.base_config['data']['sequence_lengths']['rml201801a']
            elif dataset == 'hisar':
                num_classes = len(self.base_config['hisar_class_names'])
                sequence_length = self.base_config['data']['sequence_lengths']['hisar']
            elif dataset.startswith('torchsig'):
                num_classes = len(self.base_config['torchsig_class_names'])
                sequence_length = self.base_config['data']['sequence_lengths'][dataset]
            else:
                return 0

            # 获取模型参数
            wavelet_dim = params.get('wavelet_dim', 64)
            rnn_dim = params.get('rnn_dim', 64)
            num_layers = params.get('num_layers', 2)
            num_levels = params.get('num_levels', 3)

            # 简化的参数量估算
            # 小波变换部分
            wavelet_params = wavelet_dim * 64 * 3 * num_levels  # 近似估算

            # RNN部分 (MMRNN)
            rnn_params = rnn_dim * rnn_dim * 4 * num_layers * (num_levels + 1)  # 近似估算

            # 分类器部分
            classifier_params = rnn_dim * num_classes

            total_params = wavelet_params + rnn_params + classifier_params
            return int(total_params)

        except Exception as e:
            self.logger.warning(f"参数量估算失败: {e}")
            return 0

    def create_config_with_params(self, dataset: str, params: Dict) -> str:
        """创建带有指定参数的临时配置文件"""
        # 复制基础配置
        config = copy.deepcopy(self.base_config)

        # 设置数据集
        config['data']['dataset_type'] = dataset

        # 确保数据集特定参数结构存在
        if 'dataset_specific_params' not in config['model']:
            config['model']['dataset_specific_params'] = {}

        if dataset not in config['model']['dataset_specific_params']:
            config['model']['dataset_specific_params'][dataset] = {}

        # 优化参数具有最高优先级，需要同时设置数据集特定参数和通用参数
        self.logger.info(f"🔧 应用优化参数到 {dataset} 配置:")
        for param_name, param_value in params.items():
            if param_name in ['wavelet_dim', 'rnn_dim', 'num_layers', 'num_levels', 'dropout']:
                # 1. 覆盖数据集特定参数（最高优先级）
                config['model']['dataset_specific_params'][dataset][param_name] = param_value

                # 2. 同时覆盖通用参数（确保绝对优先级）
                if param_name in ['num_levels', 'num_layers', 'dropout']:
                    config['model'][param_name] = param_value

                # 3. 对于wavelet_dim和rnn_dim，也设置到通用参数中（如果存在的话）
                if param_name in ['wavelet_dim', 'rnn_dim']:
                    config['model'][param_name] = param_value

                self.logger.info(f"   - {param_name}: {param_value} (数据集特定 + 通用)")

            elif param_name == 'batch_size':
                # 设置数据集特定的batch_size
                config['model']['dataset_specific_params'][dataset]['batch_size'] = param_value
                # 同时设置训练参数
                config['training']['batch_size'] = param_value
                self.logger.info(f"   - {param_name}: {param_value}")

            elif param_name == 'lambda_lifting':
                # 设置小波提升损失权重
                config['training']['lambda_lifting'] = param_value
                self.logger.info(f"   - {param_name}: {param_value}")

        # 使用配置的训练参数以加快优化速度
        config['training']['epochs'] = self.config['training_epochs']
        config['training']['early_stop_patience'] = self.config['early_stop_patience']

        # 设置最小训练轮数，确保有足够的训练
        if 'min_epochs' in self.config:
            config['training']['min_epochs'] = self.config['min_epochs']

        # 创建临时配置文件
        temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(config, temp_config, default_flow_style=False, allow_unicode=True)
        temp_config.close()

        # 验证配置文件内容（调试用）
        self.logger.info(f"📄 临时配置文件创建: {temp_config.name}")
        self.logger.info(f"✅ 最终数据集特定参数: {config['model']['dataset_specific_params'][dataset]}")
        self.logger.info(f"✅ 最终通用模型参数: num_layers={config['model'].get('num_layers')}, num_levels={config['model'].get('num_levels')}, dropout={config['model'].get('dropout')}")
        self.logger.info(f"✅ 最终训练参数: lambda_lifting={config['training'].get('lambda_lifting')}, batch_size={config['training'].get('batch_size')}")

        return temp_config.name

    def optimize(self):
        """执行所有数据集的分别优化"""
        self.logger.info(f"🚀 开始分别优化模式")
        self.logger.info(f"目标数据集: {self.datasets}")
        self.logger.info(f"每个数据集试验次数: {self.n_trials_per_dataset}")
        self.logger.info(f"总试验次数: {len(self.datasets)} × {self.n_trials_per_dataset} = {len(self.datasets) * self.n_trials_per_dataset}")
        self.logger.info(f"结果保存目录: {self.results_dir}")

        all_best_results = {}

        # 为每个数据集分别优化
        for i, dataset in enumerate(self.datasets, 1):
            self.logger.info(f"\n{'='*80}")
            self.logger.info(f"📊 进度: {i}/{len(self.datasets)} - 正在优化数据集: {dataset}")
            self.logger.info(f"{'='*80}")

            best_result = self.optimize_single_dataset(dataset)
            all_best_results[dataset] = best_result

            if best_result:
                self.logger.info(f"✅ {dataset} 优化完成!")
                self.logger.info(f"   最佳准确率: {best_result['best_value']:.4f}")
                self.logger.info(f"   最佳参数: {best_result['best_params']}")
            else:
                self.logger.warning(f"❌ {dataset} 优化失败")

        # 保存汇总结果
        self.save_summary_results(all_best_results)

        # 显示最终汇总
        self.display_final_summary(all_best_results)

        return all_best_results

    def save_summary_results(self, all_best_results):
        """保存汇总结果"""
        summary = {
            'optimization_mode': 'separate',
            'datasets': self.datasets,
            'n_trials_per_dataset': self.n_trials_per_dataset,
            'total_trials': len(self.datasets) * self.n_trials_per_dataset,
            'study_name': self.study_name,
            'optimization_config': self.config,
            'completion_time': datetime.now().isoformat(),
            'best_results_by_dataset': all_best_results
        }

        # 保存为YAML格式
        summary_file = os.path.join(self.results_dir, 'optimization_summary.yaml')
        with open(summary_file, 'w', encoding='utf-8') as f:
            yaml.dump(summary, f, default_flow_style=False, allow_unicode=True)

        # 保存为JSON格式
        summary_json_file = os.path.join(self.results_dir, 'optimization_summary.json')
        with open(summary_json_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        self.logger.info(f"汇总结果已保存到:")
        self.logger.info(f"  YAML格式: {summary_file}")
        self.logger.info(f"  JSON格式: {summary_json_file}")

    def display_final_summary(self, all_best_results):
        """显示最终汇总"""
        self.logger.info(f"\n🏆 分别优化最终结果汇总")
        self.logger.info("="*100)

        for dataset, best_result in all_best_results.items():
            if best_result:
                params = best_result['best_params']
                accuracy = best_result['best_value']
                trials = best_result['total_trials_so_far']

                self.logger.info(f"\n📊 {dataset}:")
                self.logger.info(f"   最佳准确率: {accuracy:.4f}")
                self.logger.info(f"   完成试验数: {trials}")
                self.logger.info(f"   最佳参数: wavelet_dim={params['wavelet_dim']}, rnn_dim={params['rnn_dim']}, num_levels={params['num_levels']}, num_layers={params['num_layers']}")
            else:
                self.logger.info(f"\n❌ {dataset}: 优化失败")

        self.logger.info(f"\n💡 查看各数据集详细结果:")
        for dataset in self.datasets:
            self.logger.info(f"   {dataset}: python quick_ranking.py {dataset}")

        self.logger.info(f"\n📁 结果文件位置: {self.results_dir}/")


def print_current_config():
    """打印当前配置"""
    print("=" * 60)
    print("当前优化配置:")
    print("=" * 60)
    print(f"研究名称: {OPTIMIZATION_CONFIG['study_name']}")
    print(f"优化模式: 分别优化")
    print(f"目标数据集: {OPTIMIZATION_CONFIG['datasets']}")

    print(f"每个数据集试验次数: {OPTIMIZATION_CONFIG['n_trials_per_dataset']}")
    print(f"总试验次数: {len(OPTIMIZATION_CONFIG['datasets'])} × {OPTIMIZATION_CONFIG['n_trials_per_dataset']} = {len(OPTIMIZATION_CONFIG['datasets']) * OPTIMIZATION_CONFIG['n_trials_per_dataset']}")
    # 估算时间 - 分别优化
    estimated_time_per_trial = OPTIMIZATION_CONFIG['training_epochs'] * 0.5  # 每个数据集单独训练
    total_estimated_time = estimated_time_per_trial * len(OPTIMIZATION_CONFIG['datasets']) * OPTIMIZATION_CONFIG['n_trials_per_dataset']

    print(f"训练轮数: {OPTIMIZATION_CONFIG['training_epochs']} (最少 {OPTIMIZATION_CONFIG.get('min_epochs', 5)} 轮)")
    print(f"早停耐心: {OPTIMIZATION_CONFIG['early_stop_patience']} 轮")
    print(f"保存频率: 每 {OPTIMIZATION_CONFIG['save_frequency']} 次试验")
    print(f"数据库存储: {'启用' if OPTIMIZATION_CONFIG['enable_database'] else '禁用'}")
    print(f"预估总时间: {total_estimated_time:.0f} 分钟 ({total_estimated_time/60:.1f} 小时)")

    print(f"要优化的参数: {OPTIMIZATION_CONFIG.get('optimize_params', ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers'])}")
    print(f"显存不足处理: {'启用' if OPTIMIZATION_CONFIG.get('skip_on_oom', True) else '禁用'}")
    print(f"最大重试次数: {OPTIMIZATION_CONFIG.get('max_oom_retries', 3)}")

    print("\n参数搜索范围:")
    for dataset in OPTIMIZATION_CONFIG['datasets']:
        if dataset in PARAMETER_RANGES:
            ranges = PARAMETER_RANGES[dataset]
            print(f"  {dataset}:")
            optimize_params = OPTIMIZATION_CONFIG.get('optimize_params', ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers', 'dropout', 'batch_size', 'lambda_lifting'])
            for param in optimize_params:
                if param in ranges:
                    param_range = ranges[param]
                    if isinstance(param_range, tuple) and len(param_range) == 2:
                        print(f"    {param}: {param_range[0]:.3f} - {param_range[1]:.3f} (连续值)")
                    else:
                        print(f"    {param}: {param_range}")
    print("=" * 60)

def main():
    """主函数"""
    print("WNN-MRNN 超参数优化器")
    print_current_config()

    # 询问是否继续
    response = input("\n是否使用上述配置开始优化? (y/n): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("优化已取消。请修改文件顶部的配置后重新运行。")
        return

    # 使用分别优化模式
    print("\n🎯 使用分别优化模式 - 为每个数据集单独寻找最优参数")
    optimizer = WNNMRNNSeparateOptimizer()
    results = optimizer.optimize()

    if results:
        print("\n" + "="*80)
        print("🎉 分别优化完成！")
        print("="*80)

        for dataset, result in results.items():
            if result:
                print(f"\n📊 {dataset}:")
                print(f"   最佳准确率: {result['best_value']:.4f}")
                print(f"   最佳参数: {result['best_params']}")
            else:
                print(f"\n❌ {dataset}: 优化失败")

        print(f"\n📁 结果保存目录: optimization_results_{OPTIMIZATION_CONFIG['study_name']}_separate")
        print("\n📋 应用建议:")
        print("1. 查看各数据集详细结果:")
        for dataset in OPTIMIZATION_CONFIG['datasets']:
            print(f"   - {dataset}: {dataset}_best_results.json, {dataset}_ranked_results.json")
        print("2. 根据需要选择合适的参数应用到 config.yaml 文件")
        print("3. 使用完整训练轮数重新训练模型")
    else:
        print("分别优化失败，请检查日志文件获取详细信息。")


if __name__ == '__main__':
    main()
